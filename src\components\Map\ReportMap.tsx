import React, { useEffect, useRef, useState } from 'react';
import L from 'leaflet';
import { Report } from '../../types';
import { format } from 'date-fns';
import { Crosshair } from 'lucide-react';

// Fix for default markers in Leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

interface ReportMapProps {
  reports: Report[];
  selectedReport?: Report | null;
  onReportSelect?: (report: Report) => void;
  height?: string;
}

const ReportMap: React.FC<ReportMapProps> = ({
  reports,
  selectedReport,
  onReportSelect,
  height = "400px",
}) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<L.Map | null>(null);
  const markersRef = useRef<L.Marker[]>([]);
  const [currentLocation, setCurrentLocation] = useState<{ lat: number; lng: number } | null>(null);

  // Map initialization
  useEffect(() => {
    if (!mapRef.current) return;

    const map = L.map(mapRef.current).setView([39.8283, -98.5795], 4);
    mapInstanceRef.current = map;

    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '© OpenStreetMap contributors',
    }).addTo(map);

    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove();
        mapInstanceRef.current = null;
      }
    };
  }, []);

  // Add markers for reports
  useEffect(() => {
    if (!mapInstanceRef.current) return;

    // Clear existing markers
    markersRef.current.forEach(marker => {
      mapInstanceRef.current?.removeLayer(marker);
    });
    markersRef.current = [];

    // Add new markers
    reports.forEach(report => {
      const marker = L.marker([report.coordinates.lat, report.coordinates.lng]);
      
      // Create custom popup content
      const popupContent = `
        <div class="p-4 max-w-sm">
          <div class="flex items-start space-x-3 mb-3">
            <div class="w-3 h-3 bg-red-500 rounded-full mt-1.5 flex-shrink-0"></div>
            <div class="flex-1 min-w-0">
              <h3 class="font-semibold text-gray-900 text-sm leading-tight mb-1">${report.title}</h3>
              <p class="text-xs text-gray-600 mb-2">${report.location}</p>
              <p class="text-xs text-gray-700 line-clamp-3">${report.description}</p>
            </div>
          </div>
          
          <div class="flex items-center justify-between pt-3 border-t border-gray-100">
            <div class="text-xs text-gray-500">
              ${format(new Date(report.createdAt), 'MMM d, yyyy')}
            </div>
            <button 
              class="view-details-btn bg-gradient-to-r from-blue-500 to-blue-600 text-white px-3 py-1.5 rounded-lg text-sm font-medium hover:from-blue-600 hover:to-blue-700 transition-all duration-200"
              data-report-id="${report.id}"
            >
              View Details
            </button>
          </div>
        </div>
      `;

      marker.bindPopup(popupContent, {
        className: 'custom-popup',
        maxWidth: 300,
        closeButton: true,
      });

      // Add click handler for view details button
      marker.on('popupopen', () => {
        const popup = marker.getPopup();
        if (popup && popup.getElement()) {
          const viewBtn = popup.getElement()?.querySelector('.view-details-btn') as HTMLButtonElement;
          if (viewBtn) {
            viewBtn.onclick = () => {
              if (onReportSelect) {
                onReportSelect(report);
              }
            };
          }
        }
      });

      marker.addTo(mapInstanceRef.current!);
      markersRef.current.push(marker);
    });
  }, [reports, onReportSelect]);

  // Handle selected report
  useEffect(() => {
    if (selectedReport && mapInstanceRef.current) {
      mapInstanceRef.current.setView(
        [selectedReport.coordinates.lat, selectedReport.coordinates.lng], 
        10
      );
    }
  }, [selectedReport]);

  // Get current location
  const getCurrentLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          setCurrentLocation({ lat: latitude, lng: longitude });
          
          if (mapInstanceRef.current) {
            mapInstanceRef.current.setView([latitude, longitude], 10);
          }
        },
        (error) => {
          console.error('Error getting location:', error);
        }
      );
    }
  };

  return (
    <div className="relative">
      {/* Current location indicator */}
      {currentLocation && (
        <div className="absolute top-4 left-4 z-20 bg-blue-500 text-white px-3 py-2 rounded-lg text-sm font-medium">
          <div className="flex items-center space-x-2">
            <Crosshair size={14} />
            <span>Your Location</span>
          </div>
        </div>
      )}

      {/* Location button */}
      <button
        onClick={getCurrentLocation}
        className="absolute top-4 right-4 z-20 bg-white text-gray-700 p-2 rounded-lg border border-gray-300 hover:bg-gray-50 transition-colors"
        title="Get current location"
      >
        <Crosshair size={20} />
      </button>

      {/* Map container */}
      <div
        ref={mapRef}
        style={{ height, width: '100%' }}
        className="rounded-lg border border-gray-300"
      />
    </div>
  );
};

export default ReportMap;
