import React, { useState, useEffect, Suspense } from 'react';
import { Link } from 'react-router-dom';
import { 
  AlertTriangle, 
  Users, 
  CheckCircle, 
  Clock, 
  ArrowRight, 
  ChevronUp,
  Shield,
  MapPin,
  MessageSquare,
  Zap,
  Heart,
  Globe
} from 'lucide-react';
import { format } from 'date-fns';

// Components
import Header from '../components/Layout/Header';
import Footer from '../components/Layout/Footer';
import ChatWidget from '../components/Chat/ChatWidget';
import ViewReportsButton from '../components/Common/ViewReportsButton';
import LoadingSpinner from '../components/Common/LoadingSpinner';

// Data
import { mockReports, mockStatistics, organizationFeatures, mockPartners } from '../data/mockData';

// Lazy load the map component
const ReportMap = React.lazy(() => import('../components/Map/ReportMap'));

const Home: React.FC = () => {
  const [showScrollTop, setShowScrollTop] = useState(false);
  const [mapLoaded, setMapLoaded] = useState(false);

  // Statistics data
  const stats = [
    {
      label: "Reports Submitted",
      value: "2,847",
      icon: AlertTriangle,
      color: "text-red-600",
    },
    {
      label: "Lives Helped",
      value: "12,450",
      icon: Users,
      color: "text-blue-600",
    },
    {
      label: "Verified Reports",
      value: "2,189",
      icon: CheckCircle,
      color: "text-green-600",
    },
    {
      label: "Response Time",
      value: "< 2hrs",
      icon: Clock,
      color: "text-purple-600",
    },
  ];

  // Scroll to top functionality
  useEffect(() => {
    const handleScroll = () => {
      setShowScrollTop(window.scrollY > 300);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  };

  // Get color classes for statistics
  const getStatColor = (color: string) => {
    switch (color) {
      case 'text-red-600':
        return 'from-red-500 to-red-600';
      case 'text-blue-600':
        return 'from-blue-500 to-blue-600';
      case 'text-green-600':
        return 'from-green-500 to-green-600';
      case 'text-purple-600':
        return 'from-purple-500 to-purple-600';
      default:
        return 'from-gray-500 to-gray-600';
    }
  };

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <main>
        {/* Hero Section */}
        <section className="relative bg-gradient-to-br from-gray-900 via-blue-900 to-indigo-900 text-white overflow-hidden">
          <div className="absolute inset-0 bg-black/10"></div>
          <div className="absolute inset-0">
            <div className="absolute top-20 left-10 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl"></div>
            <div className="absolute bottom-20 right-10 w-80 h-80 bg-purple-500/10 rounded-full blur-3xl"></div>
          </div>

          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
            <div className="text-center max-w-4xl mx-auto">
              <h1 className="text-5xl md:text-7xl font-bold leading-tight mb-8">
                Unite Communities in
                <br />
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-300 via-purple-300 to-indigo-300">
                  Times of Crisis
                </span>
              </h1>
              <p className="text-xl text-blue-100 mb-12 leading-relaxed max-w-2xl mx-auto">
                Connect with your community during emergencies. Report incidents, offer
                help, and stay informed about local disasters and relief efforts.
              </p>

              <div className="flex flex-col sm:flex-row gap-6 justify-center mb-16">
                <Link
                  to="/report/new"
                  className="group bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-10 py-5 rounded-2xl text-lg font-semibold hover:from-blue-600 hover:to-indigo-700 transition-all transform hover:-translate-y-1 hover:shadow-2xl flex items-center justify-center border border-blue-400/20"
                >
                  Report an Impact
                  <ArrowRight
                    size={20}
                    className="ml-2 group-hover:translate-x-1 transition-transform"
                  />
                </Link>
                
                <ViewReportsButton
                  variant="outline"
                  size="lg"
                  className="border-2 border-white/30 text-white hover:bg-white/10 backdrop-blur-sm"
                />
              </div>

              {/* Quick Stats */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
                {stats.map((stat, index) => (
                  <div key={index} className="text-center">
                    <div className="text-3xl md:text-4xl font-bold text-white mb-2">
                      {stat.value}
                    </div>
                    <div className="text-blue-200 text-sm">{stat.label}</div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Statistics Section */}
        <section className="py-16 bg-gray-50" aria-label="Statistics">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
                Making a Real Impact
              </h2>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                Our community-driven platform has helped thousands of people during emergencies
              </p>
            </div>

            <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
              {stats.map((stat, index) => (
                <div
                  key={index}
                  className="bg-white rounded-2xl p-8 shadow-sm border border-gray-100 hover:shadow-lg transition-shadow"
                >
                  <div className="flex items-center space-x-4">
                    <div
                      className={`p-4 rounded-2xl bg-gradient-to-br ${getStatColor(
                        stat.color
                      )} text-white shadow-lg`}
                    >
                      <stat.icon size={32} />
                    </div>
                    <div>
                      <div className="text-3xl font-bold text-gray-900">{stat.value}</div>
                      <div className="text-gray-600">{stat.label}</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-24 bg-white" aria-label="Features">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
                How DisasterWatch Works
              </h2>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                Our platform connects communities, emergency services, and volunteers to create
                a comprehensive disaster response network
              </p>
            </div>

            <div className="space-y-24">
              {organizationFeatures.map((feature, index) => (
                <div
                  key={feature.id}
                  className={`flex flex-col ${
                    index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'
                  } items-center gap-12 lg:gap-16`}
                >
                  {/* Content Side */}
                  <div className="flex-1 space-y-6">
                    <div className="flex items-center space-x-4">
                      <div className="text-6xl font-bold text-gray-200">
                        {feature.number}
                      </div>
                      <div
                        className={`p-4 rounded-2xl bg-gradient-to-br ${feature.color} text-white shadow-lg`}
                      >
                        <feature.icon size={32} />
                      </div>
                    </div>

                    <h3 className="text-3xl md:text-4xl font-bold text-gray-900 leading-tight">
                      {feature.title}
                    </h3>
                    <p className="text-lg text-gray-600 leading-relaxed">
                      {feature.description}
                    </p>

                    <div className="pt-4">
                      <Link
                        to="/reports"
                        className="inline-flex items-center text-blue-600 hover:text-blue-700 font-semibold group"
                      >
                        Learn more
                        <ArrowRight
                          size={20}
                          className="ml-2 group-hover:translate-x-1 transition-transform"
                        />
                      </Link>
                    </div>
                  </div>

                  {/* Visual Side */}
                  <div className="flex-1">
                    <div
                      className={`${feature.bgColor} rounded-3xl p-8 border border-gray-100 shadow-sm`}
                    >
                      <div className="aspect-w-16 aspect-h-12 bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                        <div className="flex items-center justify-center h-full">
                          <feature.icon size={64} className="text-gray-400" />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Interactive Map Section */}
        <section className="py-24 bg-gray-50" aria-label="Interactive Map">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
                Real-Time Disaster Map
              </h2>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                View live reports from your community and surrounding areas. Click on markers
                to see detailed information about each incident.
              </p>
            </div>

            <div className="bg-white rounded-3xl p-8 shadow-lg border border-gray-200">
              <div className="relative">
                {!mapLoaded && (
                  <div className="absolute inset-0 bg-gray-100 rounded-xl flex items-center justify-center z-10">
                    <LoadingSpinner size={32} />
                  </div>
                )}

                <Suspense fallback={<LoadingSpinner size={32} />}>
                  <ReportMap
                    reports={mockReports.slice(0, 4)}
                    height="500px"
                    onReportSelect={(report) => {
                      console.log('Selected report:', report);
                    }}
                  />
                </Suspense>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 mt-8 justify-center">
                <ViewReportsButton
                  size="lg"
                  className="flex-1 sm:flex-none transform hover:scale-105 transition-transform duration-200"
                />
                <Link
                  to="/report/new"
                  className="flex-1 sm:flex-none bg-gradient-to-r from-red-500 to-orange-500 text-white px-8 py-4 rounded-xl hover:from-red-600 hover:to-orange-600 transition-all duration-200 font-semibold flex items-center justify-center transform hover:scale-105 shadow-lg hover:shadow-xl"
                >
                  <AlertTriangle size={20} className="mr-2" />
                  Report New Incident
                </Link>
              </div>
            </div>
          </div>
        </section>

        {/* Recent Reports Section */}
        <section className="py-24 bg-white" aria-label="Recent Reports">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
                Recent Community Reports
              </h2>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                Stay informed about recent incidents in your area and see how the community
                is responding to emergencies
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
              {mockReports.slice(0, 3).map((report) => (
                <div
                  key={report.id}
                  className="bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg transition-shadow group"
                >
                  {report.images && report.images[0] && (
                    <div className="aspect-w-16 aspect-h-9 bg-gray-200">
                      <img
                        src={report.images[0]}
                        alt={report.title}
                        className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                    </div>
                  )}

                  <div className="p-6">
                    <div className="flex items-center space-x-2 mb-3">
                      <div className={`w-3 h-3 rounded-full ${
                        report.severity === 'critical' ? 'bg-red-500' :
                        report.severity === 'high' ? 'bg-orange-500' :
                        report.severity === 'medium' ? 'bg-yellow-500' : 'bg-green-500'
                      }`}></div>
                      <span className="text-sm text-gray-600 capitalize">{report.type}</span>
                      <span className="text-sm text-gray-400">•</span>
                      <span className="text-sm text-gray-600">
                        {format(new Date(report.createdAt), 'MMM d, yyyy')}
                      </span>
                    </div>

                    <h3 className="text-xl font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                      {report.title}
                    </h3>
                    <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                      {report.description}
                    </p>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <MapPin size={16} className="text-gray-400" />
                        <span className="text-sm text-gray-600">{report.location}</span>
                      </div>
                      {report.verified && (
                        <div className="flex items-center space-x-1">
                          <CheckCircle size={16} className="text-green-500" />
                          <span className="text-sm text-green-600">Verified</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className="text-center">
              <ViewReportsButton size="lg" />
            </div>
          </div>
        </section>

        {/* Partners Section */}
        <section className="py-24 bg-gray-50" aria-label="Partners">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
                Trusted Partners
              </h2>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                We work with leading organizations to ensure effective disaster response
                and community support
              </p>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8">
              {mockPartners.map((partner) => (
                <div
                  key={partner.id}
                  className="bg-white rounded-xl p-6 shadow-sm border border-gray-200 hover:shadow-md transition-shadow group"
                >
                  <img
                    src={partner.logo}
                    alt={`${partner.name} logo`}
                    className="w-full h-12 object-contain grayscale group-hover:grayscale-0 transition-all duration-300"
                  />
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Call-to-Action Section */}
        <section className="py-24 bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              Ready to Make a Difference?
            </h2>
            <p className="text-xl text-blue-100 mb-12 max-w-2xl mx-auto leading-relaxed">
              Join thousands of community members who are helping to create safer, more
              resilient communities. Every report matters, every action counts.
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <Link
                to="/report/new"
                className="group bg-white text-blue-600 px-10 py-5 rounded-2xl text-lg font-semibold hover:bg-gray-50 transition-all transform hover:-translate-y-1 hover:shadow-2xl flex items-center justify-center"
              >
                Report an Emergency
                <AlertTriangle
                  size={20}
                  className="ml-2 group-hover:scale-110 transition-transform"
                />
              </Link>

              <Link
                to="/volunteer"
                className="group border-2 border-white/30 text-white px-10 py-5 rounded-2xl text-lg font-semibold hover:bg-white/10 backdrop-blur-sm transition-all transform hover:-translate-y-1 flex items-center justify-center"
              >
                Become a Volunteer
                <Heart
                  size={20}
                  className="ml-2 group-hover:scale-110 transition-transform"
                />
              </Link>
            </div>
          </div>
        </section>
      </main>

      <Footer />
      <ChatWidget />

      {/* Scroll to Top Button */}
      {showScrollTop && (
        <button
          onClick={scrollToTop}
          className="fixed bottom-24 right-6 z-50 bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 transition-all duration-200 transform hover:scale-110"
          aria-label="Scroll to top"
        >
          <ChevronUp size={20} />
        </button>
      )}
    </div>
  );
};

export default Home;
